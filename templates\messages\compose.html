{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    إنشاء رسالة جديدة
                </h2>
                <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى صندوق الوارد
                </a>
            </div>

            <!-- نموذج إنشاء الرسالة -->
            <div class="card">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- موضوع الرسالة -->
                            <div class="col-md-8 mb-3">
                                <label for="subject" class="form-label">موضوع الرسالة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                            </div>

                            <!-- أولوية الرسالة -->
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="low">منخفضة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع الرسالة -->
                            <div class="col-md-6 mb-3">
                                <label for="message_type" class="form-label">نوع الرسالة</label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="general" selected>عامة</option>
                                    <option value="notification">إشعار</option>
                                    <option value="alert">تنبيه</option>
                                </select>
                            </div>

                            <!-- نوع المستقبلين -->
                            <div class="col-md-6 mb-3">
                                <label for="recipient_type" class="form-label">إرسال إلى</label>
                                <select class="form-select" id="recipient_type" name="recipient_type" onchange="toggleRecipientOptions()">
                                    <option value="users" selected>مستخدمين محددين</option>
                                    <option value="role">دور معين</option>
                                    {% if current_user.has_permission('send_broadcast_message') %}
                                        <option value="all">جميع المستخدمين</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>

                        <!-- خيارات المستقبلين -->
                        <div id="users_selection" class="mb-3">
                            <label for="recipients" class="form-label">اختيار المستخدمين <span class="text-danger">*</span></label>
                            <select class="form-select" id="recipients" name="recipients" multiple size="8">
                                {% for user in users %}
                                    <option value="{{ user.id }}">{{ user.name }} ({{ user.username }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اضغط Ctrl للاختيار المتعدد</div>
                        </div>

                        <div id="role_selection" class="mb-3" style="display: none;">
                            <label for="role" class="form-label">اختيار الدور</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">اختر الدور</option>
                                <option value="admin">مسؤول</option>
                                <option value="manager">مدير</option>
                                <option value="supervisor">مشرف</option>
                                <option value="user">مستخدم</option>
                            </select>
                        </div>

                        <!-- محتوى الرسالة -->
                        <div class="mb-3">
                            <label for="content" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="8" required 
                                      placeholder="اكتب محتوى الرسالة هنا..."></textarea>
                        </div>

                        <!-- المرفقات -->
                        <div class="mb-3">
                            <label for="attachments" class="form-label">المرفقات</label>
                            <input type="file" class="form-control" id="attachments" name="attachments" multiple 
                                   accept=".txt,.pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx">
                            <div class="form-text">
                                يمكنك إرفاق ملفات من الأنواع التالية: TXT, PDF, PNG, JPG, JPEG, GIF, DOC, DOCX, XLS, XLSX
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرسالة
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i>
                                    مسح الحقول
                                </button>
                            </div>
                            <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-danger">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRecipientOptions() {
    const recipientType = document.getElementById('recipient_type').value;
    const usersSelection = document.getElementById('users_selection');
    const roleSelection = document.getElementById('role_selection');
    const recipientsSelect = document.getElementById('recipients');
    const roleSelect = document.getElementById('role');

    // إخفاء جميع الخيارات أولاً
    usersSelection.style.display = 'none';
    roleSelection.style.display = 'none';
    
    // إزالة الخاصية required من جميع الحقول
    recipientsSelect.removeAttribute('required');
    roleSelect.removeAttribute('required');

    // إظهار الخيار المناسب
    if (recipientType === 'users') {
        usersSelection.style.display = 'block';
        recipientsSelect.setAttribute('required', 'required');
    } else if (recipientType === 'role') {
        roleSelection.style.display = 'block';
        roleSelect.setAttribute('required', 'required');
    }
    // إذا كان 'all' فلا نحتاج لإظهار أي خيارات إضافية
}

function clearForm() {
    if (confirm('هل أنت متأكد من مسح جميع الحقول؟')) {
        document.getElementById('subject').value = '';
        document.getElementById('content').value = '';
        document.getElementById('priority').value = 'normal';
        document.getElementById('message_type').value = 'general';
        document.getElementById('recipient_type').value = 'users';
        document.getElementById('recipients').selectedIndex = -1;
        document.getElementById('role').value = '';
        document.getElementById('attachments').value = '';
        toggleRecipientOptions();
    }
}

// تطبيق الإعدادات الأولية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleRecipientOptions();
});

// تحسين تجربة اختيار المستخدمين المتعددين
document.getElementById('recipients').addEventListener('click', function(e) {
    if (!e.ctrlKey && !e.metaKey) {
        // إذا لم يتم الضغط على Ctrl، قم بإلغاء تحديد جميع الخيارات الأخرى
        const options = this.options;
        const clickedOption = e.target;
        
        if (clickedOption.tagName === 'OPTION') {
            for (let i = 0; i < options.length; i++) {
                if (options[i] !== clickedOption) {
                    options[i].selected = false;
                }
            }
            clickedOption.selected = !clickedOption.selected;
        }
    }
});

// إضافة زر "تحديد الكل" و "إلغاء التحديد"
document.addEventListener('DOMContentLoaded', function() {
    const recipientsSelect = document.getElementById('recipients');
    const usersSelection = document.getElementById('users_selection');
    
    // إنشاء أزرار التحكم
    const controlButtons = document.createElement('div');
    controlButtons.className = 'mt-2';
    controlButtons.innerHTML = `
        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllUsers()">
            تحديد الكل
        </button>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllUsers()">
            إلغاء التحديد
        </button>
    `;
    
    usersSelection.appendChild(controlButtons);
});

function selectAllUsers() {
    const recipientsSelect = document.getElementById('recipients');
    for (let i = 0; i < recipientsSelect.options.length; i++) {
        recipientsSelect.options[i].selected = true;
    }
}

function clearAllUsers() {
    const recipientsSelect = document.getElementById('recipients');
    for (let i = 0; i < recipientsSelect.options.length; i++) {
        recipientsSelect.options[i].selected = false;
    }
}
</script>
{% endblock %}
