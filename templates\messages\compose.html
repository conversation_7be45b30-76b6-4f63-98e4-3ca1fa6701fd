{% extends "base.html" %}

{% block content %}
<style>
.user-card {
    transition: all 0.2s ease;
    border: 1px solid #dee2e6 !important;
}

.user-card:hover {
    background-color: #f8f9fa !important;
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.user-card.selected {
    background-color: #e3f2fd !important;
    border-color: #007bff !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.user-card input[type="checkbox"] {
    transform: scale(1.1);
}

.user-card label {
    margin-bottom: 0;
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    إنشاء رسالة جديدة
                </h2>
                <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة إلى صندوق الوارد
                </a>
            </div>

            <!-- نموذج إنشاء الرسالة -->
            <div class="card">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- موضوع الرسالة -->
                            <div class="col-md-8 mb-3">
                                <label for="subject" class="form-label">موضوع الرسالة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                            </div>

                            <!-- أولوية الرسالة -->
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="low">منخفضة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع الرسالة -->
                            <div class="col-md-6 mb-3">
                                <label for="message_type" class="form-label">نوع الرسالة</label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="general" selected>عامة</option>
                                    <option value="notification">إشعار</option>
                                    <option value="alert">تنبيه</option>
                                </select>
                            </div>

                            <!-- نوع المستقبلين -->
                            <div class="col-md-6 mb-3">
                                <label for="recipient_type" class="form-label">إرسال إلى</label>
                                <select class="form-select" id="recipient_type" name="recipient_type" onchange="toggleRecipientOptions()">
                                    <option value="users" selected>مستخدمين محددين</option>
                                    <option value="role">دور معين</option>
                                    {% if current_user.has_permission('send_broadcast_message') %}
                                        <option value="all">جميع المستخدمين</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>

                        <!-- خيارات المستقبلين -->
                        <div id="users_selection" class="mb-3">
                            <label class="form-label">اختيار المستخدمين <span class="text-danger">*</span></label>

                            <!-- مربع البحث -->
                            <div class="mb-2">
                                <input type="text" class="form-control form-control-sm" id="user-search" placeholder="البحث عن مستخدم...">
                            </div>

                            <div class="border rounded p-3 bg-light" style="max-height: 300px; overflow-y: auto;" id="users-container">
                                <div class="row">
                                    {% for user in users %}
                                        <div class="col-md-6 mb-2">
                                            <div class="user-card border rounded bg-white p-3 position-relative" style="cursor: pointer;">
                                                <div class="d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="checkbox" name="recipients" value="{{ user.id }}" id="user_{{ user.id }}">
                                                    <div class="me-3">
                                                        <i class="fas fa-user-circle text-secondary" style="font-size: 1.5rem;"></i>
                                                    </div>
                                                    <label class="form-check-label flex-grow-1" for="user_{{ user.id }}" style="cursor: pointer;">
                                                        <strong class="d-block text-dark">{{ user.name }}</strong>
                                                        <small class="text-muted">{{ user.username }}</small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="form-text">يمكنك اختيار عدة مستخدمين</div>
                        </div>

                        <div id="role_selection" class="mb-3" style="display: none;">
                            <label for="role" class="form-label">اختيار الدور</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">اختر الدور</option>
                                <option value="admin">مسؤول</option>
                                <option value="manager">مدير</option>
                                <option value="supervisor">مشرف</option>
                                <option value="user">مستخدم</option>
                            </select>
                        </div>

                        <!-- محتوى الرسالة -->
                        <div class="mb-3">
                            <label for="content" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="8" required 
                                      placeholder="اكتب محتوى الرسالة هنا..."></textarea>
                        </div>

                        <!-- المرفقات -->
                        <div class="mb-3">
                            <label for="attachments" class="form-label">المرفقات</label>
                            <input type="file" class="form-control" id="attachments" name="attachments" multiple 
                                   accept=".txt,.pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx">
                            <div class="form-text">
                                يمكنك إرفاق ملفات من الأنواع التالية: TXT, PDF, PNG, JPG, JPEG, GIF, DOC, DOCX, XLS, XLSX
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرسالة
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i>
                                    مسح الحقول
                                </button>
                            </div>
                            <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-danger">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRecipientOptions() {
    const recipientType = document.getElementById('recipient_type').value;
    const usersSelection = document.getElementById('users_selection');
    const roleSelection = document.getElementById('role_selection');
    const recipientsCheckboxes = document.querySelectorAll('input[name="recipients"]');
    const roleSelect = document.getElementById('role');

    // إخفاء جميع الخيارات أولاً
    usersSelection.style.display = 'none';
    roleSelection.style.display = 'none';

    // إزالة الخاصية required من جميع الحقول
    recipientsCheckboxes.forEach(checkbox => checkbox.removeAttribute('required'));
    roleSelect.removeAttribute('required');

    // إظهار الخيار المناسب
    if (recipientType === 'users') {
        usersSelection.style.display = 'block';
        // إضافة required لأول checkbox (سيتم التحقق من وجود اختيار واحد على الأقل في JavaScript)
        if (recipientsCheckboxes.length > 0) {
            recipientsCheckboxes[0].setAttribute('required', 'required');
        }
    } else if (recipientType === 'role') {
        roleSelection.style.display = 'block';
        roleSelect.setAttribute('required', 'required');
    }
    // إذا كان 'all' فلا نحتاج لإظهار أي خيارات إضافية
}

function clearForm() {
    if (confirm('هل أنت متأكد من مسح جميع الحقول؟')) {
        document.getElementById('subject').value = '';
        document.getElementById('content').value = '';
        document.getElementById('priority').value = 'normal';
        document.getElementById('message_type').value = 'general';
        document.getElementById('recipient_type').value = 'users';

        // إلغاء تحديد جميع checkboxes
        document.querySelectorAll('input[name="recipients"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.getElementById('role').value = '';
        document.getElementById('attachments').value = '';
        toggleRecipientOptions();
    }
}

// تطبيق الإعدادات الأولية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleRecipientOptions();
});

// إضافة أزرار التحكم للمستخدمين
document.addEventListener('DOMContentLoaded', function() {
    const usersSelection = document.getElementById('users_selection');

    // إنشاء أزرار التحكم
    const controlButtons = document.createElement('div');
    controlButtons.className = 'mt-2';
    controlButtons.innerHTML = `
        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllUsers()">
            تحديد الكل
        </button>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllUsers()">
            إلغاء التحديد
        </button>
    `;

    usersSelection.appendChild(controlButtons);
});

function selectAllUsers() {
    document.querySelectorAll('input[name="recipients"]').forEach(checkbox => {
        checkbox.checked = true;
        const userCard = checkbox.closest('.user-card');
        if (userCard) {
            userCard.classList.add('selected');
        }
    });
    updateSelectedCount();
}

function clearAllUsers() {
    document.querySelectorAll('input[name="recipients"]').forEach(checkbox => {
        checkbox.checked = false;
        const userCard = checkbox.closest('.user-card');
        if (userCard) {
            userCard.classList.remove('selected');
        }
    });
    updateSelectedCount();
}

// التحقق من وجود اختيار واحد على الأقل عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const recipientType = document.getElementById('recipient_type').value;

    if (recipientType === 'users') {
        const checkedBoxes = document.querySelectorAll('input[name="recipients"]:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('يجب اختيار مستخدم واحد على الأقل');
            return false;
        }
    }
});

// إضافة تأثير بصري عند تحديد/إلغاء تحديد checkbox
document.addEventListener('change', function(e) {
    if (e.target.name === 'recipients') {
        const userCard = e.target.closest('.user-card');
        if (e.target.checked) {
            userCard.classList.add('selected');
        } else {
            userCard.classList.remove('selected');
        }
    }
});

// إضافة إمكانية النقر على البطاقة بالكامل لتحديد/إلغاء تحديد المستخدم
document.addEventListener('click', function(e) {
    const userCard = e.target.closest('.user-card');
    if (userCard && !e.target.matches('input[type="checkbox"]')) {
        const checkbox = userCard.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.checked = !checkbox.checked;

            // تشغيل حدث التغيير يدوياً
            const changeEvent = new Event('change', { bubbles: true });
            checkbox.dispatchEvent(changeEvent);
        }
    }
});

// عداد المستخدمين المحددين
function updateSelectedCount() {
    const checkedBoxes = document.querySelectorAll('input[name="recipients"]:checked');
    const countElement = document.getElementById('selected-count');
    if (countElement) {
        countElement.textContent = checkedBoxes.length;
    }
}

// إضافة عداد المستخدمين المحددين
document.addEventListener('DOMContentLoaded', function() {
    const usersSelection = document.getElementById('users_selection');
    const formText = usersSelection.querySelector('.form-text');

    // إضافة عداد
    const countDiv = document.createElement('div');
    countDiv.className = 'mt-2';
    countDiv.innerHTML = `
        <small class="text-info">
            <i class="fas fa-users me-1"></i>
            المستخدمين المحددين: <span id="selected-count" class="fw-bold">0</span>
        </small>
    `;

    formText.parentNode.insertBefore(countDiv, formText);

    // تحديث العداد عند تغيير التحديد
    document.addEventListener('change', function(e) {
        if (e.target.name === 'recipients') {
            updateSelectedCount();
        }
    });
});

// وظيفة البحث في المستخدمين
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('user-search');
    const usersContainer = document.getElementById('users-container');

    if (searchInput && usersContainer) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            const userDivs = usersContainer.querySelectorAll('.col-md-6');

            userDivs.forEach(function(userDiv) {
                const label = userDiv.querySelector('label');
                const userName = label.textContent.toLowerCase();

                if (userName.includes(searchTerm)) {
                    userDiv.style.display = 'block';
                } else {
                    userDiv.style.display = 'none';
                }
            });

            // إظهار رسالة إذا لم يتم العثور على نتائج
            const visibleUsers = usersContainer.querySelectorAll('.col-md-6[style="display: block"], .col-md-6:not([style*="display: none"])');
            const noResultsMsg = usersContainer.querySelector('.no-results-message');

            if (visibleUsers.length === 0 && searchTerm !== '') {
                if (!noResultsMsg) {
                    const noResults = document.createElement('div');
                    noResults.className = 'no-results-message text-center text-muted py-3';
                    noResults.innerHTML = '<i class="fas fa-search me-2"></i>لم يتم العثور على مستخدمين';
                    usersContainer.appendChild(noResults);
                }
            } else if (noResultsMsg) {
                noResultsMsg.remove();
            }
        });
    }
});
</script>
{% endblock %}
