{% extends "base.html" %}

{% block content %}
<style>
.conversation-container {
    border-radius: 0.375rem;
}

.message-item {
    transition: background-color 0.2s ease;
}

.message-item:hover {
    background-color: #f8f9fa !important;
}

.message-item:last-child {
    border-bottom: none !important;
}

.conversation-container::-webkit-scrollbar {
    width: 8px;
}

.conversation-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.conversation-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.conversation-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.message-content .border-primary {
    border-width: 2px !important;
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-envelope-open me-2"></i>
                    عرض الرسالة
                </h2>
                <div>
                    {% if current_user.has_permission('reply_message') %}
                        <a href="{{ url_for('reply_message', id=message.id) }}" class="btn btn-primary">
                            <i class="fas fa-reply"></i>
                            رد
                        </a>
                    {% endif %}
                    <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>

            <!-- عنوان المحادثة -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">{{ message.subject }}</h5>
                            <small class="text-muted">
                                <i class="fas fa-comments me-1"></i>
                                محادثة تحتوي على {{ conversation_messages|length }} رسالة
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if message.priority == 'high' %}
                                <span class="badge bg-danger">أولوية عالية</span>
                            {% elif message.priority == 'normal' %}
                                <span class="badge bg-success">أولوية عادية</span>
                            {% elif message.priority == 'low' %}
                                <span class="badge bg-secondary">أولوية منخفضة</span>
                            {% endif %}

                            {% if message.message_type == 'general' %}
                                <span class="badge bg-secondary ms-1">عامة</span>
                            {% elif message.message_type == 'notification' %}
                                <span class="badge bg-info ms-1">إشعار</span>
                            {% elif message.message_type == 'alert' %}
                                <span class="badge bg-warning ms-1">تنبيه</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- المحادثة الكاملة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comment-dots me-2"></i>
                        المحادثة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="conversation-container" style="max-height: 600px; overflow-y: auto;">
                        {% for msg in conversation_messages %}
                            <div class="message-item border-bottom p-4 {% if msg.sender_id == current_user.id %}bg-light{% endif %}">
                                <!-- معلومات المرسل والتاريخ -->
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            {% if msg.sender_id == current_user.id %}
                                                <i class="fas fa-user-circle text-primary" style="font-size: 2rem;"></i>
                                            {% else %}
                                                <i class="fas fa-user-circle text-secondary" style="font-size: 2rem;"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <strong class="{% if msg.sender_id == current_user.id %}text-primary{% endif %}">
                                                {{ msg.sender.name }}
                                                {% if msg.sender_id == current_user.id %}
                                                    <small class="badge bg-primary">أنت</small>
                                                {% endif %}
                                            </strong>
                                            <br>
                                            <small class="text-muted">{{ msg.sender.username }}</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">
                                            {{ msg.created_at.strftime('%Y-%m-%d') }}
                                            <br>
                                            {{ msg.created_at.strftime('%H:%M') }}
                                        </small>
                                        {% if loop.first %}
                                            <br>
                                            <small class="badge bg-info">الرسالة الأصلية</small>
                                        {% else %}
                                            <br>
                                            <small class="badge bg-secondary">رد</small>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- محتوى الرسالة -->
                                <div class="message-content">
                                    <div class="border rounded p-3 {% if msg.sender_id == current_user.id %}bg-white border-primary{% else %}bg-light{% endif %}">
                                        {{ msg.content|nl2br|safe }}
                                    </div>
                                </div>

                                <!-- المرفقات -->
                                {% if msg.attachments %}
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <i class="fas fa-paperclip me-1"></i>المرفقات:
                                        </small>
                                        <div class="mt-2">
                                            {% for attachment in msg.attachments %}
                                                <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}"
                                                   class="btn btn-outline-secondary btn-sm me-2 mb-1">
                                                    <i class="fas fa-download me-1"></i>
                                                    {{ attachment.original_filename }}
                                                </a>
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- معلومات إضافية للرسالة الأصلية -->
                                {% if loop.first and msg.sender_id == current_user.id %}
                                    <div class="alert alert-info mt-3 mb-0">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>إجمالي المستقبلين:</strong> {{ msg.get_total_recipients_count() }}
                                            | <strong>لم يقرأوا بعد:</strong> {{ msg.get_unread_count() }}
                                        </small>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- نموذج الرد السريع -->
            {% if current_user.has_permission('reply_message') %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-reply me-2"></i>
                            رد سريع
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reply_message', id=message.id) }}" enctype="multipart/form-data">
                            <div class="mb-3">
                                <textarea class="form-control" name="content" rows="4" 
                                          placeholder="اكتب ردك هنا..." required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="quick_attachments" class="form-label">مرفقات (اختياري)</label>
                                <input type="file" class="form-control" id="quick_attachments" 
                                       name="attachments" multiple 
                                       accept=".txt,.pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx">
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرد
                                </button>
                                <a href="{{ url_for('reply_message', id=message.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                    رد مفصل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}

            <!-- إجراءات إضافية -->
            <div class="mt-4 text-center">
                {% if current_user.has_permission('delete_message') and recipient %}
                    <form method="POST" action="{{ url_for('delete_message', id=message.id) }}" 
                          style="display: inline;" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة من صندوق الوارد؟')">
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash"></i>
                            حذف من صندوق الوارد
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// تحديد الرسالة كمقروءة عند فتحها
{% if recipient and not recipient.is_read %}
    fetch('{{ url_for("mark_message_read", id=message.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    });
{% endif %}

// التمرير التلقائي إلى آخر رسالة في المحادثة
document.addEventListener('DOMContentLoaded', function() {
    const conversationContainer = document.querySelector('.conversation-container');
    const messageItems = document.querySelectorAll('.message-item');

    if (conversationContainer && messageItems.length > 1) {
        // التمرير إلى آخر رسالة
        const lastMessage = messageItems[messageItems.length - 1];
        setTimeout(() => {
            lastMessage.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }, 300);
    }

    // إضافة تأثير بصري للرسائل الجديدة
    messageItems.forEach((item, index) => {
        if (index > 0) { // تخطي الرسالة الأصلية
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';

            setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 100 * index);
        }
    });
});

// تحديث الوقت بشكل نسبي
function updateRelativeTime() {
    const timeElements = document.querySelectorAll('.message-time');
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            const messageTime = new Date(timestamp);
            const now = new Date();
            const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));

            let relativeTime = '';
            if (diffInMinutes < 1) {
                relativeTime = 'الآن';
            } else if (diffInMinutes < 60) {
                relativeTime = `منذ ${diffInMinutes} دقيقة`;
            } else if (diffInMinutes < 1440) {
                const hours = Math.floor(diffInMinutes / 60);
                relativeTime = `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInMinutes / 1440);
                relativeTime = `منذ ${days} يوم`;
            }

            element.textContent = relativeTime;
        }
    });
}

// تحديث الأوقات كل دقيقة
setInterval(updateRelativeTime, 60000);
</script>
{% endblock %}
