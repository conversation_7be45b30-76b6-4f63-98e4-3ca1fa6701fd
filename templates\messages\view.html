{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-envelope-open me-2"></i>
                    عرض الرسالة
                </h2>
                <div>
                    {% if current_user.has_permission('reply_message') %}
                        <a href="{{ url_for('reply_message', id=message.id) }}" class="btn btn-primary">
                            <i class="fas fa-reply"></i>
                            رد
                        </a>
                    {% endif %}
                    <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>

            <!-- تفاصيل الرسالة الأصلية -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">{{ message.subject }}</h5>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if message.priority == 'high' %}
                                <span class="badge bg-danger">أولوية عالية</span>
                            {% elif message.priority == 'normal' %}
                                <span class="badge bg-success">أولوية عادية</span>
                            {% elif message.priority == 'low' %}
                                <span class="badge bg-secondary">أولوية منخفضة</span>
                            {% endif %}
                            
                            {% if message.message_type == 'general' %}
                                <span class="badge bg-secondary ms-1">عامة</span>
                            {% elif message.message_type == 'notification' %}
                                <span class="badge bg-info ms-1">إشعار</span>
                            {% elif message.message_type == 'alert' %}
                                <span class="badge bg-warning ms-1">تنبيه</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات المرسل والتاريخ -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>المرسل:</strong> {{ message.sender.name }}
                            <br>
                            <small class="text-muted">{{ message.sender.username }}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>تاريخ الإرسال:</strong>
                            <br>
                            <small class="text-muted">
                                {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>

                    <!-- محتوى الرسالة -->
                    <div class="mb-3">
                        <div class="border rounded p-3 bg-light">
                            {{ message.content|nl2br|safe }}
                        </div>
                    </div>

                    <!-- المرفقات -->
                    {% if message.attachments %}
                        <div class="mb-3">
                            <h6><i class="fas fa-paperclip me-2"></i>المرفقات:</h6>
                            <div class="list-group">
                                {% for attachment in message.attachments %}
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-file me-2"></i>
                                            <strong>{{ attachment.original_filename }}</strong>
                                            <br>
                                            <small class="text-muted">
                                                الحجم: {{ attachment.get_file_size_formatted() }}
                                                | تاريخ الرفع: {{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </div>
                                        <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-download"></i>
                                            تحميل
                                        </a>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    <!-- معلومات إضافية للمرسل -->
                    {% if message.sender_id == current_user.id %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات الإرسال:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>إجمالي المستقبلين:</strong> {{ message.get_total_recipients_count() }}
                                </div>
                                <div class="col-md-6">
                                    <strong>لم يقرأوا بعد:</strong> {{ message.get_unread_count() }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- الردود -->
            {% if replies %}
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            الردود ({{ replies|length }})
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for reply in replies %}
                            <div class="border rounded p-3 mb-3 {% if loop.last %}mb-0{% endif %}">
                                <!-- معلومات الرد -->
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <strong>{{ reply.sender.name }}</strong>
                                        <small class="text-muted">({{ reply.sender.username }})</small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <small class="text-muted">
                                            {{ reply.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>
                                </div>

                                <!-- محتوى الرد -->
                                <div class="mb-2">
                                    {{ reply.content|nl2br|safe }}
                                </div>

                                <!-- مرفقات الرد -->
                                {% if reply.attachments %}
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-paperclip me-1"></i>المرفقات:
                                        </small>
                                        {% for attachment in reply.attachments %}
                                            <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}" 
                                               class="btn btn-outline-secondary btn-sm ms-1">
                                                <i class="fas fa-download"></i>
                                                {{ attachment.original_filename }}
                                            </a>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- نموذج الرد السريع -->
            {% if current_user.has_permission('reply_message') %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-reply me-2"></i>
                            رد سريع
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('reply_message', id=message.id) }}" enctype="multipart/form-data">
                            <div class="mb-3">
                                <textarea class="form-control" name="content" rows="4" 
                                          placeholder="اكتب ردك هنا..." required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="quick_attachments" class="form-label">مرفقات (اختياري)</label>
                                <input type="file" class="form-control" id="quick_attachments" 
                                       name="attachments" multiple 
                                       accept=".txt,.pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx">
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرد
                                </button>
                                <a href="{{ url_for('reply_message', id=message.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                    رد مفصل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}

            <!-- إجراءات إضافية -->
            <div class="mt-4 text-center">
                {% if current_user.has_permission('delete_message') and recipient %}
                    <form method="POST" action="{{ url_for('delete_message', id=message.id) }}" 
                          style="display: inline;" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة من صندوق الوارد؟')">
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash"></i>
                            حذف من صندوق الوارد
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// تحديد الرسالة كمقروءة عند فتحها
{% if recipient and not recipient.is_read %}
    fetch('{{ url_for("mark_message_read", id=message.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    });
{% endif %}
</script>
{% endblock %}
