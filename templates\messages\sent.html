{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    الرسائل المرسلة
                </h2>
                <div>
                    {% if current_user.has_permission('send_message') %}
                        <a href="{{ url_for('compose_message') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            رسالة جديدة
                        </a>
                    {% endif %}
                    <a href="{{ url_for('messages_inbox') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-inbox"></i>
                        صندوق الوارد
                    </a>
                </div>
            </div>

            <!-- شريط البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-10">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="البحث في الموضوع أو المحتوى">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الرسائل المرسلة -->
            <div class="card">
                <div class="card-body">
                    {% if messages.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr class="text-center">
                                        <th width="35%">الموضوع</th>
                                        <th width="15%">عدد المستقبلين</th>
                                        <th width="10%">النوع</th>
                                        <th width="10%">الأولوية</th>
                                        <th width="15%">تاريخ الإرسال</th>
                                        <th width="10%">حالة القراءة</th>
                                        <th width="5%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in messages.items %}
                                        <tr>
                                            <td >
                                                <a href="{{ url_for('view_message', id=message.id) }}" 
                                                   class="text-decoration-none">
                                                    <strong>{{ message.subject }}</strong>
                                                </a>
                                                {% if message.attachments %}
                                                    <i class="fas fa-paperclip text-muted ms-1" title="يحتوي على مرفقات"></i>
                                                {% endif %}
                                                {% if message.replies %}
                                                    <span class="badge bg-info ms-1">{{ message.replies|length }} رد</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-primary">{{ message.get_total_recipients_count() }}</span>
                                            </td>
                                            <td class="text-center">
                                                {% if message.message_type == 'general' %}
                                                    <span class="badge bg-secondary">عامة</span>
                                                {% elif message.message_type == 'notification' %}
                                                    <span class="badge bg-info">إشعار</span>
                                                {% elif message.message_type == 'alert' %}
                                                    <span class="badge bg-warning">تنبيه</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if message.priority == 'high' %}
                                                    <span class="badge bg-danger">عالية</span>
                                                {% elif message.priority == 'normal' %}
                                                    <span class="badge bg-success">عادية</span>
                                                {% elif message.priority == 'low' %}
                                                    <span class="badge bg-secondary">منخفضة</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                <small>
                                                    {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                </small>
                                            </td>
                                            <td>
                                                {% set unread_count = message.get_unread_count() %}
                                                {% set total_count = message.get_total_recipients_count() %}
                                                {% set read_count = total_count - unread_count %}
                                                
                                                <div class="text-center">
                                                    <small class="text-success">
                                                        <i class="fas fa-eye"></i> {{ read_count }}
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-eye-slash"></i> {{ unread_count }}
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ url_for('view_message', id=message.id) }}"
                                                       class="btn btn-outline-primary btn-sm" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if current_user.has_permission('delete_message') or message.sender_id == current_user.id %}
                                                        <form method="POST" action="{{ url_for('delete_sent_message', id=message.id) }}"
                                                              style="display: inline;"
                                                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟\nسيتم حذف الرسالة نهائياً ولا يمكن استرجاعها.')">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- التصفح -->
                        {% if messages.pages > 1 %}
                            <nav aria-label="تصفح الرسائل المرسلة">
                                <ul class="pagination justify-content-center">
                                    {% if messages.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('messages_sent', page=messages.prev_num, search=search_query) }}">السابق</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in messages.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != messages.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('messages_sent', page=page_num, search=search_query) }}">{{ page_num }}</a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if messages.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('messages_sent', page=messages.next_num, search=search_query) }}">التالي</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد رسائل مرسلة</h5>
                            <p class="text-muted">لم تقم بإرسال أي رسائل بعد.</p>
                            {% if current_user.has_permission('send_message') %}
                                <a href="{{ url_for('compose_message') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    إرسال رسالة جديدة
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
